# Mobile Automation Feature

## Overview
This document describes the new mobile automation feature that extends the existing web automation functionality to support mobile application testing.

## New Endpoints

The following new endpoints have been added to support mobile automation:

### POST `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Save mobile automation steps for a test case
- **Request Body**: `AutomationMobileTestCaseDto`
- **Response**: Created mobile automation test case
- **Status Codes**: 
  - 201: Mobile automation steps saved successfully
  - 404: Test case not found

### GET `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Get mobile automation steps for a test case
- **Response**: Mobile automation test case or empty steps array
- **Status Codes**:
  - 200: Mobile automation steps retrieved successfully
  - 404: Test case not found

### PATCH `/projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile`
- **Purpose**: Update mobile automation steps for a test case
- **Request Body**: `AutomationMobileTestCaseDto`
- **Response**: Updated mobile automation test case
- **Status Codes**:
  - 200: Mobile automation steps updated successfully
  - 404: Test case not found

## Database Schema

### New Table: `automation_mobile_test_cases`

```sql
CREATE TABLE automation_mobile_test_cases (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  testCaseId UUID NOT NULL REFERENCES test_cases(id) ON DELETE CASCADE,
  steps JSON NOT NULL,
  createdAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updatedAt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX IDX_automation_mobile_test_cases_testCaseId 
ON automation_mobile_test_cases(testCaseId);
```

## Data Transfer Objects (DTOs)

### AutomationMobileTestCaseDto
Main DTO for mobile automation test cases containing:
- `testCaseId`: UUID of the associated test case
- `steps`: Array of `MobileAutomationStep` objects

### MobileAutomationStep
Individual mobile automation step containing:
- `step`: Step number
- `stepName`: Description of the step
- `target`: Mobile element selector (xpath, id, accessibility id, etc.)
- `value`: Value for the action (app package, text input, etc.)
- `action`: Mobile action type (launchApp, tap, swipe, input, assertText, etc.)
- `platform`: Mobile platform (android, ios)
- `orientation`: Device orientation (portrait, landscape)
- `coordinates`: Tap/swipe coordinates in JSON format
- `prompt`: AI-assisted step description
- `Actions`: Multiple actions in JSON format
- `fileUrl`: URL of uploaded files
- `fileId`: Unique file identifier
- Various UI flags for interaction dropdowns

## Mobile-Specific Features

### Mobile Actions
The mobile automation supports various mobile-specific actions:
- `launchApp`: Launch a mobile application
- `tap`: Tap on mobile elements
- `swipe`: Swipe gestures
- `input`: Text input on mobile keyboards
- `assertText`: Assert text content on mobile screens
- And more standard mobile testing actions

### Platform Support
- **Android**: Support for Android-specific selectors and actions
- **iOS**: Support for iOS-specific selectors and actions

### Device Orientation
- **Portrait**: Vertical device orientation
- **Landscape**: Horizontal device orientation

### Coordinate-based Actions
Support for coordinate-based interactions when element selectors are not available.

## File Structure

### New Files Created:
1. `src/test-cases/automation-mobile-test-case.entity.ts` - Entity for mobile automation
2. `src/test-cases/dto/automation-mobile-test-case.dto.ts` - DTOs for mobile automation
3. `src/migrations/1691234567891-CreateAutomationMobileTestCasesTable.ts` - Database migration
4. `src/test-cases/test/mobile-automation.test.ts` - Unit tests

### Modified Files:
1. `src/test-cases/test-cases.controller.ts` - Added mobile automation endpoints
2. `src/test-cases/test-cases.service.ts` - Added mobile automation service methods
3. `src/test-cases/test-cases.module.ts` - Added mobile automation entity to module

## Service Methods

### saveMobileAutomationSteps()
- Creates or updates mobile automation steps for a test case
- Validates project access and test case existence
- Returns the saved mobile automation test case

### getMobileAutomationSteps()
- Retrieves mobile automation steps for a test case
- Returns null if no mobile automation exists
- Validates project access and test case existence

### updateMobileAutomationSteps()
- Updates existing mobile automation steps
- Throws NotFoundException if mobile automation doesn't exist
- Validates project access and test case existence

## Usage Example

```typescript
// Save mobile automation steps
const mobileAutomationDto = {
  testCaseId: "test-case-uuid",
  steps: [
    {
      step: 1,
      stepName: "Launch mobile app",
      action: "launchApp",
      value: "com.example.app",
      platform: "android",
      orientation: "portrait"
    },
    {
      step: 2,
      stepName: "Tap login button",
      action: "tap",
      target: "//android.widget.Button[@text='Login']",
      platform: "android"
    }
  ]
};

// POST /projects/{projectId}/test-cases/tcId/{tcId}/automation-mobile
```

## Testing

Unit tests have been created in `src/test-cases/test/mobile-automation.test.ts` to verify:
- Saving mobile automation steps
- Retrieving mobile automation steps
- Updating mobile automation steps
- Error handling for non-existent test cases

## Migration

To apply the database changes, run the migration:
```bash
npm run migration:run
```

This will create the new `automation_mobile_test_cases` table with the appropriate foreign key constraints and indexes.
