import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query, Res, UseInterceptors, UploadedFile, BadRequestException, NotFoundException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiConsumes, ApiBody, ApiParam } from '@nestjs/swagger';
import { TestCasesService } from './test-cases.service';
import { CreateTestCaseDto } from './dto/create-test-case.dto';
import { CreateFolderDto } from './dto/create-folder.dto';
import { MoveItemDto } from './dto/move-item.dto';
import { TestCase, TestCaseType } from './test-case.entity';
import { TestCaseFolder } from './folder.entity';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guards';
import { Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import * as Multer from 'multer';
import { ImportJob } from './import-job.entity';
import { AutomationTestCaseDto } from './dto/automation-test-case.dto';
import { AutomationMobileTestCaseDto } from './dto/automation-mobile-test-case.dto';
import { UploadAutomationFileDto } from './dto/upload-automation-file.dto';


@ApiTags('test-cases')
@ApiBearerAuth()
@Controller('projects/:projectId/test-cases')
@UseGuards(JwtAuthGuard)
export class TestCasesController {
  constructor(private readonly testCasesService: TestCasesService) {}

  @Get('folders')
  @ApiOperation({ summary: 'Get all folders for a project' })
  @ApiResponse({ status: 200, description: 'List of folders', type: [TestCaseFolder] })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  getFolders(
    @Param('projectId') projectId: string,
    @Request() req,
  ) {
    return this.testCasesService.getFolders(projectId, req.user.companyId);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new test case' })
  @ApiResponse({ status: 201, description: 'Test case created successfully', type: TestCase })
  create(
    @Param('projectId') projectId: string,
    @Request() req,
    @Body() createTestCaseDto: CreateTestCaseDto,
  ) {
    return this.testCasesService.create(projectId, req.user.id, createTestCaseDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get test cases for a project (paginated or all)' })
  @ApiResponse({ status: 200, description: 'List of test cases', type: [TestCase] })
  findAll(
    @Param('projectId') projectId: string,
    @Request() req,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('folderId') folderId?: string,
    @Query('all') all?: boolean,
    @Query('sortField') sortField?: string,
    @Query('sortDirection') sortDirection?: 'ASC' | 'DESC',
    @Query('priorityFilter') priorityFilter?: string | string[],
    @Query('platformFilter') platformFilter?: string | string[],
    @Query('testCaseTypeFilter') testCaseTypeFilter?: string | string[],
    @Query('typeFilter') typeFilter?: string | string[],
    @Query('tagFilter') tagFilter?: string | string[]
  ) {
    // Sanitize search input if provided
    const sanitizedSearch = search ? search.trim() : undefined;

    // Normalize sort direction
    const normalizedSortDirection = sortDirection === 'DESC' ? 'DESC' : 'ASC';

    // Process filters
    const filters = {
      priority: Array.isArray(priorityFilter) ? priorityFilter : priorityFilter ? [priorityFilter] : [],
      platform: Array.isArray(platformFilter) ? platformFilter : platformFilter ? [platformFilter] : [],
      testCaseType: Array.isArray(testCaseTypeFilter) ? testCaseTypeFilter : testCaseTypeFilter ? [testCaseTypeFilter] : [],
      type: Array.isArray(typeFilter) ? typeFilter : typeFilter ? [typeFilter] : [],
      tagIds: Array.isArray(tagFilter) ? tagFilter : tagFilter ? [tagFilter] : []
    };

    // If 'all' parameter is true, skip pagination
    if (all === true) {
      return this.testCasesService.findAllWithoutPagination(
        projectId,
        req.user.id,
        {
          search: sanitizedSearch,
          folderId,
          sortField,
          sortDirection: normalizedSortDirection,
          filters
        }
      );
    }

    // Otherwise, use pagination (with defaults)
    return this.testCasesService.findAll(
      projectId,
      req.user.id,
      {
        page: page ? +page : 1,
        limit: limit ? +limit : 50,
        search: sanitizedSearch,
        folderId,
        sortField,
        sortDirection: normalizedSortDirection,
        filters
      }
    );
  }

  @Post('import')
  @ApiOperation({ summary: 'Import test cases from CSV (legacy endpoint)' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Test cases imported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file format or content' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  @UseInterceptors(FileInterceptor('file'))
  async importTestCases(
    @Param('projectId') projectId: string,
    @UploadedFile() file: Multer.File,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    const fileContent = file.buffer.toString('utf-8');
    const result = await this.testCasesService.importFromCsv(projectId, req.user.id, fileContent);

    if (result.errors.length > 0) {
      throw new BadRequestException(result);
    }
    return result;
  }

  @Post('import/start')
  @ApiOperation({ summary: 'Start an asynchronous import process' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'Import job started successfully' })
  @ApiResponse({ status: 400, description: 'Invalid file format or content' })
  @UseInterceptors(FileInterceptor('file'))
  async startImportJob(
    @Param('projectId') projectId: string,
    @UploadedFile() file: Multer.File,
    @Request() req,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    // Start the import job and return the job ID
    const importJob = await this.testCasesService.startImportJob(
      projectId,
      req.user.id,
      file
    );

    return {
      jobId: importJob.id,
      status: importJob.status,
      message: 'Import job started successfully'
    };
  }

  @Get('import/status/:jobId')
  @ApiOperation({ summary: 'Get the status of an import job' })
  @ApiResponse({ status: 200, description: 'Import job status' })
  @ApiResponse({ status: 404, description: 'Import job not found' })
  async getImportJobStatus(
    @Param('projectId') projectId: string,
    @Param('jobId') jobId: string,
    @Request() req,
  ) {
    const importJob = await this.testCasesService.getImportJobStatus(
      projectId,
      jobId,
      req.user.id
    );

    if (!importJob) {
      throw new NotFoundException(`Import job with ID ${jobId} not found`);
    }

    return {
      id: importJob.id,
      status: importJob.status.toLowerCase(),
      progress: importJob.progress,
      message: importJob.message,
      error: importJob.error,
      totalRows: importJob.totalRows,
      processedRows: importJob.processedRows,
      successRows: importJob.successRows,
      errorRows: importJob.errorRows,
      createdAt: importJob.createdAt,
      updatedAt: importJob.updatedAt
    };
  }

  @Get('export')
  @ApiOperation({ summary: 'Export test cases' })
  @ApiQuery({ name: 'format', enum: ['csv'], description: 'Export format' })
  @ApiResponse({ status: 200, description: 'Test cases exported successfully' })
  @ApiResponse({ status: 400, description: 'Invalid export format' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Project not found' })
  async exportTestCases(
    @Param('projectId') projectId: string,
    @Query('format') format: string,
    @Request() req,
    @Res() res: Response,
  ) {
    if (format !== 'csv') {
      return res.status(400).json({ message: 'Unsupported export format' });
    }

    const csvData = await this.testCasesService.exportToCsv(projectId, req.user.id);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename=test-cases-${projectId}.csv`);
    return res.send(csvData);
  }

  @Get('structure')
  @ApiOperation({ summary: 'Get folder structure with test cases' })
  @ApiResponse({ status: 200, description: 'Folder structure retrieved successfully' })
  getFolderStructure(
    @Param('projectId') projectId: string,
    @Request() req,
  ) {
    return this.testCasesService.getFolderStructure(projectId, req.user.companyId);
  }

  @Get('filter-options')
  @ApiOperation({ summary: 'Get all available filter options for test cases' })
  @ApiResponse({ status: 200, description: 'Filter options including all types and tags' })
  getFilterOptions(
    @Param('projectId') projectId: string,
    @Request() req,
  ) {
    return this.testCasesService.getFilterOptions(projectId, req.user.id);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a specific test case' })
  @ApiResponse({ status: 200, description: 'Test case found', type: TestCase })
  findOne(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testCasesService.findOne(id, projectId, req.user.id);
  }

  @Get('security/zap-report')
  @ApiOperation({ summary: 'Fetch ZAP security report' })
  @ApiResponse({ status: 200, description: 'ZAP report retrieved successfully' })
  @ApiResponse({ status: 404, description: 'ZAP report not available' })
  async getZapReport(@Request() req, @Query('testCaseId') testCaseId?: string) {
    return this.testCasesService.fetchZapReport(req.user.id, testCaseId);
  }

  @Post('security/save-report-url')
  @ApiOperation({ summary: 'Save ZAP security report URL to database' })
  @ApiResponse({ status: 200, description: 'Security report URL saved successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request data' })
  async saveSecurityReportUrl(
    @Request() req,
    @Body() body: { testCaseId: string; securityReportUrl: string }
  ) {
    await this.testCasesService.saveSecurityReportUrl(body.testCaseId, body.securityReportUrl);
    return { success: true, message: 'Security report URL saved successfully' };
  }

  @Get('tcId/:tcId')
  @ApiOperation({ summary: 'Get a specific test case by tcId' })
  @ApiResponse({ status: 200, description: 'Test case found', type: TestCase })
  async findByTcId(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: number,
    @Request() req,
  ) {
    const testCase = await this.testCasesService.findByTcId(Number(tcId), projectId, req.user.id);
    // Include the projectId in the response
    return {
      ...testCase,
      projectId
    };
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update a test case' })
  @ApiResponse({ status: 200, description: 'Test case updated successfully', type: TestCase })
  update(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
    @Body() updateTestCaseDto: Partial<CreateTestCaseDto>,
  ) {
    return this.testCasesService.update(id, projectId, req.user.id, updateTestCaseDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a test case' })
  @ApiResponse({ status: 200, description: 'Test case deleted successfully' })
  remove(
    @Param('projectId') projectId: string,
    @Param('id') id: string,
    @Request() req,
  ) {
    return this.testCasesService.remove(id, projectId, req.user.id);
  }

  @Post('bulk-delete')
  bulkRemove(
  @Param('projectId') projectId: string,
  @Body() dto: { ids: string[] },
  @Request() req,
) {
  return this.testCasesService.bulkRemove(dto.ids, projectId, req.user.id);
}


  @Post('folders')
  @ApiOperation({ summary: 'Create a new folder' })
  @ApiResponse({ status: 201, description: 'Folder created successfully', type: TestCaseFolder })
  createFolder(
    @Param('projectId') projectId: string,
    @Request() req,
    @Body() createFolderDto: CreateFolderDto,
  ) {
    return this.testCasesService.createFolder(projectId, req.user.id, createFolderDto);
  }

  @Delete('folders/:folderId')
  @ApiOperation({ summary: 'Delete a folder and its contents' })
  @ApiResponse({ status: 200, description: 'Folder deleted successfully' })
  deleteFolder(
    @Param('projectId') projectId: string,
    @Param('folderId') folderId: string,
    @Request() req,
  ) {
    return this.testCasesService.deleteFolder(projectId, folderId, req.user.id);
  }

  @Post('move')
  @ApiOperation({ summary: 'Move a test case or folder' })
  @ApiResponse({ status: 200, description: 'Item moved successfully' })
  moveItem(
    @Param('projectId') projectId: string,
    @Request() req,
    @Body() moveItemDto: MoveItemDto,
  ) {
    return this.testCasesService.moveItem(projectId, req.user.id, moveItemDto);
  }

  @Post('tcId/:tcId/automation')
  @ApiOperation({ summary: 'Save automation steps for a test case' })
  @ApiResponse({ status: 201, description: 'Automation steps saved successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async saveAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() automationDto: AutomationTestCaseDto,
    @Request() req,
  ) {
    return this.testCasesService.saveAutomationSteps(projectId, tcId, automationDto, req.user.id);
  }

  @Get('tcId/:tcId/automation')
  @ApiOperation({ summary: 'Get automation steps for a test case' })
  @ApiResponse({ status: 200, description: 'Automation steps retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async getAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Request() req,
  ) {
    const automationSteps = await this.testCasesService.getAutomationSteps(projectId, tcId, req.user.id);
    if (!automationSteps) {
      return { steps: [] };
    }
    return automationSteps;
  }

  @Patch('tcId/:tcId/automation')
  @ApiOperation({ summary: 'Update automation steps for a test case' })
  @ApiResponse({ status: 200, description: 'Automation steps updated successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async updateAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() automationDto: AutomationTestCaseDto,
    @Request() req,
  ) {
    return this.testCasesService.updateAutomationSteps(projectId, tcId, automationDto, req.user.id);
  }

  @Post('tcId/:tcId/automation-mobile')
  @ApiOperation({ summary: 'Save mobile automation steps for a test case' })
  @ApiResponse({ status: 201, description: 'Mobile automation steps saved successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async saveMobileAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() automationMobileDto: AutomationMobileTestCaseDto,
    @Request() req,
  ) {
    return this.testCasesService.saveMobileAutomationSteps(projectId, tcId, automationMobileDto, req.user.id);
  }

  @Get('tcId/:tcId/automation-mobile')
  @ApiOperation({ summary: 'Get mobile automation steps for a test case' })
  @ApiResponse({ status: 200, description: 'Mobile automation steps retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async getMobileAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Request() req,
  ) {
    const mobileAutomationSteps = await this.testCasesService.getMobileAutomationSteps(projectId, tcId, req.user.id);
    if (!mobileAutomationSteps) {
      return { steps: [] };
    }
    return mobileAutomationSteps;
  }

  @Patch('tcId/:tcId/automation-mobile')
  @ApiOperation({ summary: 'Update mobile automation steps for a test case' })
  @ApiResponse({ status: 200, description: 'Mobile automation steps updated successfully' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  async updateMobileAutomationSteps(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() automationMobileDto: AutomationMobileTestCaseDto,
    @Request() req,
  ) {
    return this.testCasesService.updateMobileAutomationSteps(projectId, tcId, automationMobileDto, req.user.id);
  }

  @Post(':tcId/automation/upload-file')
  @ApiOperation({ summary: 'Upload a file for automation test case step' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'The file to upload'
        },
        stepId: {
          type: 'string',
          description: 'The step ID within the automation test case',
          example: '1'
        },
        testCaseId: {
          type: 'string',
          format: 'uuid',
          description: 'The test case ID this file belongs to',
          example: '123e4567-e89b-12d3-a456-************'
        }
      },
      required: ['file', 'stepId', 'testCaseId']
    }
  })
  @ApiResponse({
    status: 201,
    description: 'File uploaded successfully',
    schema: {
      type: 'object',
      properties: {
        fileUrl: {
          type: 'string',
          description: 'The Google Cloud Storage URL of the uploaded file',
          example: 'gs://agentq/test-data/automation-files/project123/testcase456/step-1/file123-document.pdf'
        },
        fileId: {
          type: 'string',
          description: 'Unique identifier for the uploaded file',
          example: 'testcase456-step-1-1691234567890'
        },
        replacedFiles: {
          type: 'number',
          description: 'Number of existing files that were replaced',
          example: 1
        }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - missing file or invalid parameters' })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadAutomationFile(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @UploadedFile() file: Multer.File,
    @Body() body: { stepId: string; testCaseId: string },
    @Request() req: any,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    return this.testCasesService.uploadAutomationFile(
      projectId,
      tcId,
      body.stepId,
      body.testCaseId,
      file,
      req.user.id
    );
  }

  @Delete(':tcId/automation/files/:fileId')
  @ApiOperation({ summary: 'Delete an uploaded file for automation test case step' })
  @ApiParam({
    name: 'fileId',
    description: 'The unique file identifier to delete',
    example: 'testcase456-step-1-1691234567890'
  })
  @ApiResponse({
    status: 200,
    description: 'File deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'File deleted successfully'
        },
        fileId: {
          type: 'string',
          example: 'testcase456-step-1-1691234567890'
        }
      }
    }
  })
  @ApiResponse({ status: 404, description: 'File not found' })
  @ApiResponse({ status: 403, description: 'Unauthorized to delete this file' })
  async deleteAutomationFile(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Param('fileId') fileId: string,
    @Request() req: any,
  ) {
    return this.testCasesService.deleteAutomationFile(
      projectId,
      tcId,
      fileId,
      req.user.id
    );
  }



  @Patch('tcId/:tcId/type')
  @ApiOperation({ summary: 'Update test case type by tcId' })
  @ApiResponse({ status: 200, description: 'Test case type updated successfully', type: TestCase })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['testCaseType'],
      properties: {
        testCaseType: {
          type: 'string',
          enum: ['manual', 'automation'],
          description: 'The new test case type',
          example: 'automation'
        }
      }
    }
  })
  async updateTestCaseType(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() updateDto: { testCaseType: TestCaseType },
    @Request() req,
  ) {
    return this.testCasesService.updateTestCaseType(
      projectId, 
      Number(tcId), 
      updateDto.testCaseType,
      req.user.id
    );
  }

  @Patch('tcId/:tcId/automation-by-agentq')
  @ApiOperation({ summary: 'Update automation by AgentQ flag for a test case' })
  @ApiResponse({ status: 200, description: 'Automation flag updated successfully', type: TestCase })
  @ApiResponse({ status: 404, description: 'Test case not found' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['automationByAgentq'],
      properties: {
        automationByAgentq: {
          type: 'boolean',
          description: 'Whether the test case is automated by AgentQ',
          example: true
        }
      }
    }
  })
  async updateAutomationByAgentq(
    @Param('projectId') projectId: string,
    @Param('tcId') tcId: string,
    @Body() updateDto: { automationByAgentq: boolean },
    @Request() req,
  ) {
    return this.testCasesService.updateAutomationByAgentq(
      projectId, 
      Number(tcId), 
      updateDto.automationByAgentq,
      req.user.id
    );
  }
}
