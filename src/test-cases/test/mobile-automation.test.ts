import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TestCasesService } from '../test-cases.service';
import { AutomationMobileTestCase } from '../automation-mobile-test-case.entity';
import { TestCase } from '../test-case.entity';
import { AutomationMobileTestCaseDto } from '../dto/automation-mobile-test-case.dto';
import { ProjectsService } from '../../projects/projects.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { StorageService } from '../storage.service';

describe('Mobile Automation Test Cases', () => {
  let service: TestCasesService;
  let mobileAutomationRepository: Repository<AutomationMobileTestCase>;
  let testCaseRepository: Repository<TestCase>;

  const mockMobileAutomationRepository = {
    findOne: jest.fn(),
    save: jest.fn(),
    create: jest.fn(),
  };

  const mockTestCaseRepository = {
    findOne: jest.fn(),
  };

  const mockProjectsService = {
    findOne: jest.fn(),
  };

  const mockEventEmitter = {
    emit: jest.fn(),
  };

  const mockStorageService = {
    uploadFile: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TestCasesService,
        {
          provide: getRepositoryToken(AutomationMobileTestCase),
          useValue: mockMobileAutomationRepository,
        },
        {
          provide: getRepositoryToken(TestCase),
          useValue: mockTestCaseRepository,
        },
        {
          provide: ProjectsService,
          useValue: mockProjectsService,
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter,
        },
        {
          provide: StorageService,
          useValue: mockStorageService,
        },
        // Add other required repositories as mocks
        {
          provide: getRepositoryToken('AutomationTestCase'),
          useValue: {},
        },
        {
          provide: getRepositoryToken('TestCaseFolder'),
          useValue: {},
        },
        {
          provide: getRepositoryToken('Tag'),
          useValue: {},
        },
        {
          provide: getRepositoryToken('ImportJob'),
          useValue: {},
        },
        {
          provide: getRepositoryToken('User'),
          useValue: { findOne: jest.fn().mockResolvedValue({ companyId: 'test-company' }) },
        },
        {
          provide: getRepositoryToken('TempTestResult'),
          useValue: {},
        },
        {
          provide: getRepositoryToken('AutomationFile'),
          useValue: {},
        },
      ],
    }).compile();

    service = module.get<TestCasesService>(TestCasesService);
    mobileAutomationRepository = module.get<Repository<AutomationMobileTestCase>>(
      getRepositoryToken(AutomationMobileTestCase),
    );
    testCaseRepository = module.get<Repository<TestCase>>(getRepositoryToken(TestCase));
  });

  describe('saveMobileAutomationSteps', () => {
    it('should save mobile automation steps for a test case', async () => {
      const projectId = 'test-project-id';
      const tcId = '1';
      const userId = 'test-user-id';
      const mobileAutomationDto: AutomationMobileTestCaseDto = {
        testCaseId: 'test-case-id',
        steps: [
          {
            step: 1,
            stepName: 'Launch mobile app',
            action: 'launchApp',
            value: 'com.example.app',
            target: '',
            prompt: '',
            showInteractionDropdown: false,
            showAssertionDropdown: false,
            showPromptField: false,
            platform: 'android',
            orientation: 'portrait',
          },
        ],
      };

      const mockTestCase = {
        id: 'test-case-id',
        tcId: 1,
        projectId,
      };

      const mockMobileAutomation = {
        id: 'mobile-automation-id',
        testCaseId: 'test-case-id',
        steps: mobileAutomationDto.steps,
      };

      mockProjectsService.findOne.mockResolvedValue({ id: projectId });
      mockTestCaseRepository.findOne.mockResolvedValue(mockTestCase);
      mockMobileAutomationRepository.findOne.mockResolvedValue(null);
      mockMobileAutomationRepository.save.mockResolvedValue(mockMobileAutomation);

      const result = await service.saveMobileAutomationSteps(
        projectId,
        tcId,
        mobileAutomationDto,
        userId,
      );

      expect(result).toEqual(mockMobileAutomation);
      expect(mockMobileAutomationRepository.save).toHaveBeenCalled();
    });
  });

  describe('getMobileAutomationSteps', () => {
    it('should retrieve mobile automation steps for a test case', async () => {
      const projectId = 'test-project-id';
      const tcId = '1';
      const userId = 'test-user-id';

      const mockTestCase = {
        id: 'test-case-id',
        tcId: 1,
        projectId,
      };

      const mockMobileAutomation = {
        id: 'mobile-automation-id',
        testCaseId: 'test-case-id',
        steps: [
          {
            step: 1,
            stepName: 'Launch mobile app',
            action: 'launchApp',
            value: 'com.example.app',
          },
        ],
      };

      mockProjectsService.findOne.mockResolvedValue({ id: projectId });
      mockTestCaseRepository.findOne.mockResolvedValue(mockTestCase);
      mockMobileAutomationRepository.findOne.mockResolvedValue(mockMobileAutomation);

      const result = await service.getMobileAutomationSteps(projectId, tcId, userId);

      expect(result).toEqual(mockMobileAutomation);
      expect(mockMobileAutomationRepository.findOne).toHaveBeenCalledWith({
        where: { testCaseId: mockTestCase.id },
      });
    });
  });
});
