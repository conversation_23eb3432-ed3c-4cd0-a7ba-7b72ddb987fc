import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestCaseTypesService } from './test-case-types.service';
import { TestCasesController } from './test-cases.controller';
import { TestCasesService } from './test-cases.service';
import { TestCase } from './test-case.entity';
import { AutomationTestCase } from './automation-test-case.entity';
import { AutomationMobileTestCase } from './automation-mobile-test-case.entity';

import { TestCaseFolder } from './folder.entity';
import { TagsService } from './tag.service';
import { TagsController } from './tag.controller';
import { TestCaseTypesController } from './test-case-types.controller';
import { Tag } from './tag.entity';
import { TestCaseType } from './test-case-type.entity';
import { ImportJob } from './import-job.entity';
import { ProjectsModule } from '../projects/projects.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { User } from '../users/user.entity';
import { UsersModule } from '../users/user.module';
import { TempTestResult } from '../temp-test-results/temp-test-result.entity';
import { StorageService } from './storage.service';
import { AutomationFile } from './automation-file.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([TestCase, TestCaseFolder, Tag, AutomationTestCase, AutomationMobileTestCase, TestCaseType, ImportJob, User, TempTestResult, AutomationFile]),
    ProjectsModule,
    UsersModule,
    EventEmitterModule, // Just import the module without calling forRoot()
  ],
  controllers: [TestCasesController, TagsController, TestCaseTypesController],
  providers: [TestCasesService, TagsService, TestCaseTypesService, StorageService],
  exports: [TestCasesService],
})
export class TestCasesModule {}
